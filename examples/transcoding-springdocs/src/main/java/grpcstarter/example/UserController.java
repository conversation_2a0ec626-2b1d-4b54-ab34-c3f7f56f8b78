package grpcstarter.example;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import user.v1.BatchCreateUsersRequest;
import user.v1.BatchCreateUsersResponse;
import user.v1.CreateUserRequest;
import user.v1.CreateUserResponse;
import user.v1.GetUserResponse;
import user.v1.ListUsersResponse;
import user.v1.PatchUserRequest;
import user.v1.PatchUserResponse;
import user.v1.SearchUsersRequest;
import user.v1.SearchUsersResponse;
import user.v1.UpdateUserResponse;
import user.v1.User;

/**
 * REST Controller demonstrating Spring Boot + gRPC + OpenAPI integration
 * with comprehensive test cases for mixed Java and Protobuf types
 */
@RestController
@Tag(name = "User Management", description = "APIs for user management and mixed type testing")
public class UserController {

    // ========== Core User CRUD Operations ==========

    @PostMapping("/api/v1/users")
    @Operation(summary = "Create a new user", description = "Creates a new user with the provided information")
    public CreateUserResponse createUser(@RequestBody CreateUserRequest request) {
        User user = request.getUser().toBuilder()
                .setUserId(UUID.randomUUID().toString())
                .build();

        return CreateUserResponse.newBuilder()
                .setUser(user)
                .setMessage("User created successfully")
                .build();
    }

    @GetMapping("/api/v1/users/{userId}")
    @Operation(summary = "Get user by ID", description = "Retrieves a user by their unique identifier")
    public GetUserResponse getUser(@PathVariable String userId, @RequestParam(required = false) List<String> fields) {
        // Mock user data
        User user = User.newBuilder()
                .setUserId(userId)
                .setUsername("john_doe")
                .setEmail("<EMAIL>")
                .setFirstName("John")
                .setLastName("Doe")
                .setGender(User.Gender.MALE)
                .setAge(30)
                .setStatus(User.UserStatus.ACTIVE)
                .build();

        return GetUserResponse.newBuilder().setUser(user).build();
    }

    @PutMapping("/api/v1/users/{userId}")
    @Operation(summary = "Update user", description = "Updates an existing user with new information")
    public UpdateUserResponse updateUser(@PathVariable String userId, @RequestBody User user) {
        User updatedUser = user.toBuilder().setUserId(userId).build();

        return UpdateUserResponse.newBuilder()
                .setUser(updatedUser)
                .setMessage("User updated successfully")
                .build();
    }

    @PatchMapping("/api/v1/users/{userId}")
    @Operation(summary = "Partially update user", description = "Updates specific fields of an existing user")
    public PatchUserResponse patchUser(@PathVariable String userId, @RequestBody PatchUserRequest request) {
        // Mock updated user
        User user = User.newBuilder()
                .setUserId(userId)
                .setUsername(request.getUsername())
                .setEmail(request.getEmail())
                .setFirstName(request.getFirstName())
                .setLastName(request.getLastName())
                .setGender(request.getGender())
                .setAge(request.getAge())
                .setStatus(request.getStatus())
                .build();

        return PatchUserResponse.newBuilder()
                .setUser(user)
                .setMessage("User partially updated successfully")
                .build();
    }

    @DeleteMapping("/api/v1/users/{userId}")
    @Operation(summary = "Delete user", description = "Deletes a user by their unique identifier")
    public void deleteUser(@PathVariable String userId, @RequestParam(defaultValue = "true") boolean softDelete) {
        // Mock deletion logic
    }

    @GetMapping("/api/v1/users")
    @Operation(summary = "List users", description = "Retrieves a paginated list of users with optional filtering")
    public ListUsersResponse listUsers(
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String pageToken,
            @RequestParam(required = false) User.UserStatus statusFilter,
            @RequestParam(required = false) User.Gender genderFilter,
            @RequestParam(required = false) String searchQuery,
            @RequestParam(required = false) List<String> sortBy) {
        // Mock user list
        List<User> users = new ArrayList<>();
        users.add(User.newBuilder()
                .setUserId("user1")
                .setUsername("alice")
                .setEmail("<EMAIL>")
                .setFirstName("Alice")
                .setLastName("Smith")
                .setGender(User.Gender.FEMALE)
                .setAge(25)
                .setStatus(User.UserStatus.ACTIVE)
                .build());

        return ListUsersResponse.newBuilder()
                .addAllUsers(users)
                .setNextPageToken("next_page_token")
                .setTotalCount(1)
                .build();
    }

    @PostMapping("/api/v1/users/search")
    @Operation(summary = "Search users", description = "Searches users based on complex criteria")
    public SearchUsersResponse searchUsers(@RequestBody SearchUsersRequest request) {
        // Mock search results
        List<User> users = new ArrayList<>();
        Map<String, Integer> facets = new HashMap<>();
        facets.put("gender_male", 10);
        facets.put("gender_female", 8);

        return SearchUsersResponse.newBuilder()
                .addAllUsers(users)
                .setTotalCount(0)
                .putAllFacets(facets)
                .build();
    }

    @PostMapping("/api/v1/users/batch")
    @Operation(summary = "Batch create users", description = "Creates multiple users in a single operation")
    public BatchCreateUsersResponse batchCreateUsers(@RequestBody BatchCreateUsersRequest request) {
        List<User> createdUsers = new ArrayList<>();
        List<String> failedUserIds = new ArrayList<>();

        return BatchCreateUsersResponse.newBuilder()
                .addAllCreatedUsers(createdUsers)
                .addAllFailedUserIds(failedUserIds)
                .setMessage("Batch operation completed")
                .setSuccessCount(0)
                .setFailureCount(0)
                .build();
    }

    // ========== Java Bean 嵌套 protobuf message 测试用例 ==========

    /**
     * Java POJO 包含 protobuf User 消息
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserWrapper {
        private String wrapperName;
        private User user;
        private LocalDateTime createdAt;
        private boolean isActive;
        private String description;
    }

    @PostMapping("/api/test/user-wrapper")
    @Operation(
            summary = "Test Java Bean with nested protobuf User",
            description = "Tests Java POJO containing protobuf User message")
    public UserWrapper createUserWrapper(@RequestBody UserWrapper userWrapper) {
        return userWrapper.toBuilder().createdAt(LocalDateTime.now()).build();
    }

    /**
     * Java POJO 包含 protobuf PhoneNumber 嵌套消息
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {
        private String contactId;
        private User.PhoneNumber primaryPhone;
        private List<User.PhoneNumber> additionalPhones;
        private String notes;
        private LocalDateTime lastUpdated;
    }

    @PostMapping("/api/test/contact-info")
    @Operation(
            summary = "Test Java Bean with nested protobuf PhoneNumber",
            description = "Tests Java POJO containing protobuf PhoneNumber nested message")
    public ContactInfo createContactInfo(@RequestBody ContactInfo contactInfo) {
        return contactInfo.toBuilder()
                .contactId(UUID.randomUUID().toString())
                .lastUpdated(LocalDateTime.now())
                .build();
    }

    /**
     * Java POJO 包含 protobuf Address 嵌套消息
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private String locationId;
        private User.Address address;
        private Double latitude;
        private Double longitude;
        private Set<String> tags;
        private LocalDateTime verifiedAt;
    }

    @PutMapping("/api/test/location/{locationId}")
    @Operation(
            summary = "Test Java Bean with nested protobuf Address",
            description = "Tests Java POJO containing protobuf Address nested message")
    public LocationInfo updateLocation(@PathVariable String locationId, @RequestBody LocationInfo locationInfo) {
        return locationInfo.toBuilder()
                .locationId(locationId)
                .verifiedAt(LocalDateTime.now())
                .build();
    }

    // ========== Java Bean 嵌套 protobuf enum 测试用例 ==========

    /**
     * Java POJO 包含 protobuf enum 类型
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserProfile {
        private String profileId;
        private String displayName;
        private User.Gender gender;
        private User.UserStatus status;
        private User.Theme preferredTheme;
        private Integer age;
        private List<User.Gender> allowedGenders;
        private Map<String, User.UserStatus> statusHistory;
        private Set<User.Theme> availableThemes;
    }

    @PostMapping("/api/test/user-profile")
    @Operation(
            summary = "Test Java Bean with protobuf enums",
            description = "Tests Java POJO containing various protobuf enum types")
    public UserProfile createUserProfile(@RequestBody UserProfile userProfile) {
        return userProfile.toBuilder().profileId(UUID.randomUUID().toString()).build();
    }

    @GetMapping("/api/test/user-profile/{profileId}")
    @Operation(
            summary = "Get user profile with enum filtering",
            description = "Tests GET endpoint with protobuf enum query parameters")
    public UserProfile getUserProfile(
            @PathVariable String profileId,
            @RequestParam(required = false) User.Gender filterGender,
            @RequestParam(required = false) User.UserStatus filterStatus,
            @RequestParam(required = false) List<User.Theme> themes) {
        return UserProfile.builder()
                .profileId(profileId)
                .gender(filterGender)
                .status(filterStatus)
                .availableThemes(themes != null ? new HashSet<>(themes) : null)
                .build();
    }

    // ========== 混合类型组合测试用例 ==========

    /**
     * Java 基本类型与 protobuf 类型的组合
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MixedTypeRequest {
        private String requestId;
        private Integer priority;
        private BigDecimal amount;
        private Boolean isUrgent;
        private User user;
        private User.Gender preferredGender;
        private User.UserStatus targetStatus;
        private List<String> tags;
        private Map<String, Object> metadata;
        private LocalDateTime scheduledAt;
    }

    @PostMapping("/api/test/mixed-type")
    @Operation(
            summary = "Test mixed Java and protobuf types",
            description = "Tests combination of Java basic types with protobuf types")
    public MixedTypeRequest processMixedType(@RequestBody MixedTypeRequest request) {
        return request.toBuilder()
                .requestId(UUID.randomUUID().toString())
                .scheduledAt(LocalDateTime.now())
                .build();
    }

    /**
     * Java 集合类型包含 protobuf 对象
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CollectionWrapper {
        private String collectionId;
        private List<User> users;
        private Set<User.Gender> availableGenders;
        private Map<String, User> userMap;
        private Map<User.Gender, List<User>> usersByGender;
        private List<User.PhoneNumber> allPhoneNumbers;
        private Map<User.UserStatus, Integer> statusCounts;
        private Set<User.Address> uniqueAddresses;
    }

    @PostMapping("/api/test/collection-wrapper")
    @Operation(
            summary = "Test Java collections with protobuf objects",
            description = "Tests Java collection types containing protobuf objects")
    public CollectionWrapper createCollectionWrapper(@RequestBody CollectionWrapper wrapper) {
        return wrapper.toBuilder().collectionId(UUID.randomUUID().toString()).build();
    }

    @GetMapping("/api/test/collection-wrapper/{collectionId}")
    @Operation(
            summary = "Get collection with filtering",
            description = "Tests GET endpoint with complex protobuf enum filtering")
    public CollectionWrapper getCollectionWrapper(
            @PathVariable String collectionId,
            @RequestParam(required = false) List<User.Gender> filterGenders,
            @RequestParam(required = false) List<User.UserStatus> filterStatuses,
            @RequestParam(defaultValue = "10") Integer limit) {
        return CollectionWrapper.builder()
                .collectionId(collectionId)
                .availableGenders(filterGenders != null ? new HashSet<>(filterGenders) : null)
                .build();
    }

    /**
     * 嵌套层级更深的复杂对象结构
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NestedComplexStructure {
        private String structureId;
        private UserWrapper userWrapper;
        private List<ContactInfo> contactInfoList;
        private Map<String, LocationInfo> locationMap;
        private NestedLevel1 nestedLevel1;
        private LocalDateTime createdAt;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NestedLevel1 {
        private String level1Id;
        private User user;
        private NestedLevel2 nestedLevel2;
        private List<UserProfile> profiles;
        private Map<User.Gender, CollectionWrapper> genderCollections;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NestedLevel2 {
        private String level2Id;
        private Map<User.UserStatus, List<User>> usersByStatus;
        private List<Map<String, User.PhoneNumber>> phoneNumberMaps;
        private Set<User.Address> addressSet;
        private Map<User.Theme, UserProfile> themeProfiles;
    }

    @PostMapping("/api/test/nested-complex")
    @Operation(
            summary = "Test deeply nested complex structures",
            description = "Tests deeply nested object structures with mixed types")
    public NestedComplexStructure createNestedComplex(@RequestBody NestedComplexStructure structure) {
        return structure.toBuilder()
                .structureId(UUID.randomUUID().toString())
                .createdAt(LocalDateTime.now())
                .build();
    }

    @PatchMapping("/api/test/nested-complex/{structureId}")
    @Operation(
            summary = "Partially update nested complex structure",
            description = "Tests PATCH operation on complex nested structures")
    public NestedComplexStructure updateNestedComplex(
            @PathVariable String structureId, @RequestBody NestedComplexStructure structure) {
        return structure.toBuilder()
                .structureId(structureId)
                .createdAt(LocalDateTime.now())
                .build();
    }

    // ========== protobuf message 包含 Java 普通类型字段的测试用例 ==========

    /**
     * 混合 protobuf 和 Java 类型的响应包装器
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProtobufJavaResponse {
        private String responseId;
        private LocalDateTime timestamp;
        private Integer statusCode;
        private String message;
        private User userData;
        private CreateUserResponse protobufResponse;
        private List<String> warnings;
        private Map<String, Object> additionalData;
        private BigDecimal processingCost;
        private Boolean isSuccess;
    }

    @PostMapping("/api/test/protobuf-java-response")
    @Operation(
            summary = "Test protobuf message with Java fields",
            description = "Tests response containing both protobuf messages and Java types")
    public ProtobufJavaResponse createProtobufJavaResponse(@RequestBody User user) {
        CreateUserResponse protobufResponse = CreateUserResponse.newBuilder()
                .setUser(user)
                .setMessage("User processed successfully")
                .build();

        return ProtobufJavaResponse.builder()
                .responseId(UUID.randomUUID().toString())
                .timestamp(LocalDateTime.now())
                .statusCode(200)
                .message("Success")
                .userData(user)
                .protobufResponse(protobufResponse)
                .processingCost(new BigDecimal("0.05"))
                .isSuccess(true)
                .build();
    }
}
